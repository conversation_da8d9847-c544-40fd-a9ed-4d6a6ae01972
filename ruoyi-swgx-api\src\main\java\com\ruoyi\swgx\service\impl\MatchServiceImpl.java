package com.ruoyi.swgx.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.swgx.domain.*;
import com.ruoyi.swgx.domain.dto.MatchProgressDto;
import com.ruoyi.swgx.domain.dto.MatchRequestDto;
import com.ruoyi.swgx.mapper.DpFpxxMapper;
import com.ruoyi.swgx.mapper.PpRwMapper;
import com.ruoyi.swgx.mapper.YwDjFpPpMapper;
import com.ruoyi.swgx.mapper.YwDjxxMapper;
import com.ruoyi.swgx.service.IMatchService;
import com.ruoyi.swgx.service.IMatchStatusService;
import com.ruoyi.swgx.service.IYwFpsqdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 匹配服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Service
public class MatchServiceImpl implements IMatchService {
    
    @Autowired
    private PpRwMapper ppRwMapper;
    
    @Autowired
    private YwDjxxMapper ywDjxxMapper;
    
    @Autowired
    private DpFpxxMapper dpFpxxMapper;
    

    
    @Autowired
    private YwDjFpPpMapper ywDjFpPpMapper;

    @Autowired
    private IYwFpsqdService ywFpsqdService;

    @Autowired
    private IMatchStatusService matchStatusService;

    /** 任务进度缓存 */
    private final Map<String, MatchProgressDto> taskProgressCache = new ConcurrentHashMap<>();
    
    @Override
    @Transactional
    public String startMatchTask(MatchRequestDto request) {
        log.info("启动匹配任务，请求参数：{}", request);

        // 创建匹配任务
        PpRw task = new PpRw();
        task.setId(IdUtils.fastSimpleUUID());
        task.setRwMc("匹配任务-" + DateUtils.dateTimeNow());
        task.setRwLx(request.getMatchType());
        task.setRwZt("PENDING");
        task.setDjIds(String.join(",", request.getDjIds()));
        task.setPpGzIds(String.join(",", request.getMatchRules()));
        task.setPpCs(0);
        task.setCgCs(0);
        task.setSbCs(0);
        task.setCreateBy(ShiroUtils.getLoginName());
        task.setCreateTime(DateUtils.getNowDate());
        
        // 保存任务
        ppRwMapper.insertPpRw(task);
        
        // 初始化进度缓存
        MatchProgressDto progress = new MatchProgressDto();
        progress.setTaskId(task.getId());
        progress.setStatus("PENDING");
        progress.setTotalCount(request.getDjIds().size());
        progress.setSuccessCount(0);
        progress.setFailedCount(0);
        progress.setProgress(0);
        progress.setStatusDesc("任务已创建，等待执行");
        taskProgressCache.put(task.getId(), progress);
        
        // 异步执行匹配任务
        executeMatchTaskAsync(task, request);
        
        return task.getId();
    }
    
    @Override
    public MatchProgressDto getMatchProgress(String taskId) {
        MatchProgressDto progress = taskProgressCache.get(taskId);
        if (progress == null) {
            // 从数据库查询任务状态
            PpRw task = ppRwMapper.selectPpRwById(taskId);
            if (task != null) {
                progress = new MatchProgressDto();
                progress.setTaskId(taskId);
                progress.setStatus(task.getRwZt());
                progress.setTotalCount(task.getPpCs());
                progress.setSuccessCount(task.getCgCs());
                progress.setFailedCount(task.getSbCs());
                progress.setProgress(calculateProgress(task.getCgCs(), task.getSbCs(), task.getPpCs()));
                progress.setStatusDesc(getStatusDescription(task.getRwZt()));
                progress.setErrorMsg(task.getErrorMsg());
            }
        }
        return progress;
    }
    
    @Override
    @Transactional
    public boolean cancelMatchTask(String taskId) {
        try {
            PpRw task = ppRwMapper.selectPpRwById(taskId);
            if (task != null && "RUNNING".equals(task.getRwZt())) {
                task.setRwZt("CANCELLED");
                task.setJsSj(DateUtils.getNowDate());
                task.setUpdateBy(ShiroUtils.getLoginName());
                task.setUpdateTime(DateUtils.getNowDate());
                ppRwMapper.updatePpRw(task);
                
                // 更新进度缓存
                MatchProgressDto progress = taskProgressCache.get(taskId);
                if (progress != null) {
                    progress.setStatus("CANCELLED");
                    progress.setStatusDesc("任务已取消");
                }
                
                return true;
            }
        } catch (Exception e) {
            log.error("取消匹配任务失败，任务ID：{}", taskId, e);
        }
        return false;
    }
    
    @Async
    public void executeMatchTaskAsync(PpRw task, MatchRequestDto request) {
        try {
            executeMatchTask(task);
        } catch (Exception e) {
            log.error("异步执行匹配任务失败，任务ID：{}", task.getId(), e);
            updateTaskStatus(task.getId(), "FAILED", e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public void executeMatchTask(PpRw task) {
        log.info("开始执行匹配任务，任务ID：{}", task.getId());
        
        try {
            // 更新任务状态为执行中
            updateTaskStatus(task.getId(), "RUNNING", null);
            
            // 获取单据列表
            String[] djIdArray = task.getDjIds().split(",");
            List<String> djIds = Arrays.asList(djIdArray);
            
            // 初始化电票明细匹配状态
            initInvoiceDetailMatchStatus();
            
            int totalCount = 0;
            int successCount = 0;
            int failedCount = 0;
            
            for (String djId : djIds) {
                try {
                    // 执行单个单据的匹配
                    boolean result = matchSingleDocument(djId, task);
                    if (result) {
                        successCount++;
                    } else {
                        failedCount++;
                    }
                    totalCount++;
                    
                    // 更新进度
                    updateProgress(task.getId(), totalCount, successCount, failedCount, djIds.size());
                    
                } catch (Exception e) {
                    log.error("匹配单据失败，单据ID：{}，任务ID：{}", djId, task.getId(), e);
                    failedCount++;
                    totalCount++;
                    updateProgress(task.getId(), totalCount, successCount, failedCount, djIds.size());
                }
            }
            
            // 更新任务完成状态
            task.setPpCs(totalCount);
            task.setCgCs(successCount);
            task.setSbCs(failedCount);
            task.setJsSj(DateUtils.getNowDate());
            task.setUpdateBy(ShiroUtils.getLoginName());
            task.setUpdateTime(DateUtils.getNowDate());
            
            if (failedCount == 0) {
                updateTaskStatus(task.getId(), "SUCCESS", null);
            } else if (successCount == 0) {
                updateTaskStatus(task.getId(), "FAILED", "所有匹配都失败");
            } else {
                updateTaskStatus(task.getId(), "SUCCESS", "部分匹配成功");
            }
            
            log.info("匹配任务执行完成，任务ID：{}，总数：{}，成功：{}，失败：{}", 
                    task.getId(), totalCount, successCount, failedCount);
                    
        } catch (Exception e) {
            log.error("执行匹配任务失败，任务ID：{}", task.getId(), e);
            updateTaskStatus(task.getId(), "FAILED", e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public void initInvoiceDetailMatchStatus() {
        log.info("开始初始化电票明细匹配状态");

        try {
            // 统计总的电票明细数量
            int totalDetails = dpFpxxMapper.countAllDetails();
            log.info("电票明细总数：{}", totalDetails);

            // 查询所有未初始化的电票明细
            List<DpFpxxMx> uninitializedDetails = dpFpxxMapper.selectUninitializedDetails();
            log.info("待初始化的电票明细数量：{}", uninitializedDetails.size());

            if (uninitializedDetails.isEmpty()) {
                log.info("所有电票明细已初始化，无需处理");
                return;
            }

            int processedCount = 0;
            int skippedCount = 0;

            for (DpFpxxMx detail : uninitializedDetails) {
                try {
                    // 直接更新发票明细表的匹配状态字段（简化版）
                    detail.setSySl(detail.getSpsl()); // 剩余数量等于原始数量
                    detail.setSyJe(detail.getJe());   // 剩余金额等于原始金额
                    detail.setUpdateTime(DateUtils.getNowDate());

                    dpFpxxMapper.updateDpFpxxMx(detail);
                    processedCount++;

                    // 每处理100条记录输出一次进度日志
                    if ((processedCount + skippedCount) % 100 == 0) {
                        log.info("已处理 {} / {} 条电票明细（成功: {}, 跳过: {})",
                                processedCount + skippedCount, uninitializedDetails.size(),
                                processedCount, skippedCount);
                    }

                } catch (Exception e) {
                    log.error("初始化电票明细匹配状态失败 - 明细ID: {}", detail.getId(), e);
                    skippedCount++;
                }
            }

            log.info("电票明细匹配状态初始化完成，总计: {} 条，成功: {} 条，跳过: {} 条",
                    uninitializedDetails.size(), processedCount, skippedCount);

        } catch (Exception e) {
            log.error("初始化电票明细匹配状态失败", e);
            throw new RuntimeException("初始化电票明细匹配状态失败", e);
        }
    }
    
    /**
     * 匹配单个单据（优化版本）
     */
    private boolean matchSingleDocument(String djId, PpRw task) {
        log.info("开始处理单据，单据ID：{}", djId);

        try {
            // 查询单据及其明细
            YwDjxx document = ywDjxxMapper.selectYwDjxxById(djId);
            if (document == null) {
                log.warn("单据不存在，单据ID：{}", djId);
                return false;
            }

            // 检查单据匹配状态，如果已经完全匹配则直接返回
            if ("2".equals(document.getPpzt())) {
                log.info("单据已完全匹配，无需重复匹配 - 单据ID: {}", djId);
                return true;
            }

            List<YwDjmx> documentDetails = ywDjxxMapper.selectYwDjmxByDjId(djId);
            if (documentDetails.isEmpty()) {
                log.warn("单据明细为空，单据ID：{}", djId);
                return false;
            }

            // 过滤出未完全匹配的明细
            List<YwDjmx> unmatchedDetails = documentDetails.stream()
                .filter(detail -> !"2".equals(detail.getPpZt())) // 排除已完全匹配的明细
                .filter(detail -> detail.getSySl() == null || detail.getSySl().compareTo(BigDecimal.ZERO) > 0) // 剩余数量为null或>0
                .collect(Collectors.toList());

            if (unmatchedDetails.isEmpty()) {
                log.info("所有明细已完全匹配，更新单据状态 - 单据ID: {}", djId);
                updateDocumentStatus(djId, "2"); // 更新为完全匹配
                return true;
            }

            log.info("单据ID: {}, 总明细数: {}, 未匹配明细数: {}", djId, documentDetails.size(), unmatchedDetails.size());

            // 根据单据类型判断处理方式
            if (isPositiveInvoiceDocument(document)) {
                // 正数发票单据，直接生成发票申请单
                log.info("单据{}为正数发票类型，直接生成发票申请单", djId);
                return processPositiveInvoiceDocument(djId, document, unmatchedDetails);
            } else {
                // 红冲单据，执行匹配逻辑
                log.info("单据{}为红冲类型，执行电票匹配逻辑", djId);
                return processRedFlushDocument(djId, document, unmatchedDetails);
            }

        } catch (Exception e) {
            log.error("处理单据失败，单据ID：{}", djId, e);
            return false;
        }
    }

    /**
     * 判断是否为正数发票单据
     */
    private boolean isPositiveInvoiceDocument(YwDjxx document) {
        // 根据单据类型判断：djlx='ZS'为正数发票，其他为红冲
        return "ZS".equals(document.getDjlx());
    }

    /**
     * 处理正数发票单据
     */
    private boolean processPositiveInvoiceDocument(String djId, YwDjxx document, List<YwDjmx> documentDetails) {
        try {
            log.info("开始处理正数发票单据，单据ID：{}", djId);

            // 直接生成发票申请单，无需匹配
            generatePositiveInvoiceApplication(djId, document, documentDetails);

            // 更新单据状态为已处理
            updateDocumentStatus(djId, "1"); // 假设1表示已处理

            log.info("正数发票单据处理完成，单据ID：{}", djId);
            return true;

        } catch (Exception e) {
            log.error("处理正数发票单据失败，单据ID：{}", djId, e);
            return false;
        }
    }

    /**
     * 处理红冲单据
     */
    private boolean processRedFlushDocument(String djId, YwDjxx document, List<YwDjmx> documentDetails) {
        try {
            // 查询可匹配的电票明细
            List<DpFpxxMx> availableInvoiceDetails = dpFpxxMapper.selectAvailableDetailsForMatch(
                document.getGmfNsrsbh());

            if (availableInvoiceDetails.isEmpty()) {
                // 检查是否有电票明细但未初始化
                List<DpFpxxMx> uninitializedDetails = dpFpxxMapper.selectUninitializedDetails();
                if (!uninitializedDetails.isEmpty()) {
                    log.warn("存在未初始化的电票明细，请先执行初始化操作，单据ID：{}", djId);
                    throw new RuntimeException("存在未初始化的电票明细，请先在电票管理页面点击'初始化匹配状态'按钮");
                } else {
                    log.warn("没有可匹配的电票明细，单据ID：{}", djId);
                    return false;
                }
            }

            // 执行智能匹配算法
            List<MatchResult> matchResults = performMatching(documentDetails, availableInvoiceDetails);

            // 保存匹配结果
            saveMatchResults(djId, matchResults);

            // 更新单据匹配状态
            updateDocumentMatchStatus(djId);

            // 生成红冲发票申请单（按发票分组）
            if (!matchResults.isEmpty()) {
                generateRedFlushInvoiceApplicationsByInvoice(djId, document, matchResults);
            }

            return true;

        } catch (Exception e) {
            log.error("处理红冲单据失败，单据ID：{}", djId, e);
            return false;
        }
    }
    
    /**
     * 执行匹配算法（新版本 - 基于发票级别的智能匹配）
     */
    private List<MatchResult> performMatching(List<YwDjmx> documentDetails, List<DpFpxxMx> invoiceDetails) {
        log.info("开始执行发票级别智能匹配算法，单据明细数量：{}，可用电票明细数量：{}",
                documentDetails.size(), invoiceDetails.size());

        // 按发票ID分组电票明细
        Map<String, List<DpFpxxMx>> invoiceDetailsMap = invoiceDetails.stream()
                .collect(Collectors.groupingBy(DpFpxxMx::getFpid));

        // 计算每张发票的评分
        List<InvoiceScore> invoiceScores = calculateInvoiceScores(documentDetails, invoiceDetailsMap);

        // 按评分降序排序
        invoiceScores.sort((a, b) -> Double.compare(b.getTotalScore(), a.getTotalScore()));

        log.info("发票评分完成，共{}张发票参与评分", invoiceScores.size());
        for (InvoiceScore score : invoiceScores.subList(0, Math.min(5, invoiceScores.size()))) {
            log.info("发票ID：{}，总评分：{:.2f}，覆盖度：{:.2f}，数量满足度：{:.2f}，精确匹配度：{:.2f}，优先级：{:.2f}",
                    score.getInvoiceId(), score.getTotalScore(), score.getCoverageScore(),
                    score.getQuantityScore(), score.getExactMatchScore(), score.getPriorityScore());
        }

        // 执行贪心匹配算法
        return performGreedyMatching(documentDetails, invoiceScores);
    }

    /**
     * 计算每张发票的综合评分
     */
    private List<InvoiceScore> calculateInvoiceScores(List<YwDjmx> documentDetails,
                                                     Map<String, List<DpFpxxMx>> invoiceDetailsMap) {
        List<InvoiceScore> scores = new ArrayList<>();

        for (Map.Entry<String, List<DpFpxxMx>> entry : invoiceDetailsMap.entrySet()) {
            String invoiceId = entry.getKey();
            List<DpFpxxMx> invDetails = entry.getValue();

            InvoiceScore score = calculateSingleInvoiceScore(documentDetails, invoiceId, invDetails);
            if (score.getTotalScore() > 0) {
                scores.add(score);
            }
        }

        return scores;
    }

    /**
     * 计算单张发票的综合评分
     */
    private InvoiceScore calculateSingleInvoiceScore(List<YwDjmx> documentDetails,
                                                    String invoiceId, List<DpFpxxMx> invoiceDetails) {
        InvoiceScore score = new InvoiceScore();
        score.setInvoiceId(invoiceId);
        score.setInvoiceDetails(invoiceDetails);

        // 1. 计算商品覆盖度评分（权重40%）
        double coverageScore = calculateCoverageScore(documentDetails, invoiceDetails);
        score.setCoverageScore(coverageScore);

        // 2. 计算数量满足度评分（权重35%）
        double quantityScore = calculateQuantityScore(documentDetails, invoiceDetails);
        score.setQuantityScore(quantityScore);

        // 3. 计算精确匹配度评分（权重20%）
        double exactMatchScore = calculateExactMatchScore(documentDetails, invoiceDetails);
        score.setExactMatchScore(exactMatchScore);

        // 4. 计算优先级评分（权重5%）
        double priorityScore = calculatePriorityScore(invoiceDetails);
        score.setPriorityScore(priorityScore);

        // 计算总评分
        double totalScore = coverageScore * 0.4 + quantityScore * 0.35 +
                           exactMatchScore * 0.2 + priorityScore * 0.05;
        score.setTotalScore(totalScore);

        return score;
    }

    /**
     * 计算商品覆盖度评分
     * 评分 = 电票包含的业务单据商品种类数 / 业务单据总商品种类数
     */
    private double calculateCoverageScore(List<YwDjmx> documentDetails, List<DpFpxxMx> invoiceDetails) {
        if (documentDetails.isEmpty()) return 0.0;

        Set<String> docProducts = documentDetails.stream()
                .map(detail -> generateProductKey(detail.getSpMc(), detail.getDj(), detail.getSlv()))
                .collect(Collectors.toSet());

        Set<String> matchedProducts = new HashSet<>();
        for (DpFpxxMx invDetail : invoiceDetails) {
            String productKey = generateProductKey(invDetail.getSpmc(), invDetail.getDj(), invDetail.getSl());
            if (docProducts.contains(productKey)) {
                matchedProducts.add(productKey);
            }
        }

        return (double) matchedProducts.size() / docProducts.size();
    }

    /**
     * 计算数量满足度评分
     * 评分 = 电票能满足的业务单据商品数量比例
     */
    private double calculateQuantityScore(List<YwDjmx> documentDetails, List<DpFpxxMx> invoiceDetails) {
        if (documentDetails.isEmpty()) return 0.0;

        BigDecimal totalRequiredQuantity = BigDecimal.ZERO;
        BigDecimal totalSatisfiedQuantity = BigDecimal.ZERO;

        for (YwDjmx docDetail : documentDetails) {
            BigDecimal requiredQuantity = docDetail.getSySl() != null ? docDetail.getSySl() : docDetail.getSl();
            if (requiredQuantity == null) continue;

            totalRequiredQuantity = totalRequiredQuantity.add(requiredQuantity);

            // 查找匹配的电票明细
            for (DpFpxxMx invDetail : invoiceDetails) {
                if (isExactMatch(docDetail, invDetail)) {
                    BigDecimal availableQuantity = invDetail.getSySl() != null ? invDetail.getSySl() : invDetail.getSpsl();
                    if (availableQuantity != null && availableQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal satisfiedQuantity = requiredQuantity.min(availableQuantity);
                        totalSatisfiedQuantity = totalSatisfiedQuantity.add(satisfiedQuantity);
                    }
                    break; // 找到匹配就跳出
                }
            }
        }

        if (totalRequiredQuantity.compareTo(BigDecimal.ZERO) == 0) return 0.0;
        return totalSatisfiedQuantity.divide(totalRequiredQuantity, 4, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 计算精确匹配度评分
     * 评分 = 精确匹配的商品数量 / 总商品数量
     */
    private double calculateExactMatchScore(List<YwDjmx> documentDetails, List<DpFpxxMx> invoiceDetails) {
        if (documentDetails.isEmpty()) return 0.0;

        int exactMatchCount = 0;
        for (YwDjmx docDetail : documentDetails) {
            for (DpFpxxMx invDetail : invoiceDetails) {
                if (isExactMatch(docDetail, invDetail)) {
                    exactMatchCount++;
                    break; // 找到匹配就跳出
                }
            }
        }

        return (double) exactMatchCount / documentDetails.size();
    }

    /**
     * 计算优先级评分
     * 基于开票日期，越近的发票得分越高
     */
    private double calculatePriorityScore(List<DpFpxxMx> invoiceDetails) {
        if (invoiceDetails.isEmpty()) return 0.0;

        // 这里简化处理，实际可以根据发票的开票日期计算
        // 暂时返回固定值，后续可以优化
        return 0.5;
    }

    /**
     * 生成商品唯一标识
     */
    private String generateProductKey(String spmc, BigDecimal dj, BigDecimal sl) {
        return String.format("%s_%s_%s",
                spmc != null ? spmc : "",
                dj != null ? dj.toString() : "",
                sl != null ? sl.toString() : "");
    }

    /**
     * 执行贪心匹配算法（动态重新评分版本）
     */
    private List<MatchResult> performGreedyMatching(List<YwDjmx> documentDetails, List<InvoiceScore> invoiceScores) {
        List<MatchResult> results = new ArrayList<>();
        List<YwDjmx> remainingDetails = new ArrayList<>(documentDetails);
        List<InvoiceScore> remainingInvoiceScores = new ArrayList<>(invoiceScores);

        log.info("开始执行动态重新评分的贪心匹配算法，剩余单据明细数量：{}", remainingDetails.size());

        int round = 1;
        while (!remainingDetails.isEmpty() && !remainingInvoiceScores.isEmpty()) {
            // 重新计算所有剩余发票的评分（基于当前剩余明细和发票状态）
            List<InvoiceScore> currentRoundScores = recalculateInvoiceScores(remainingDetails, remainingInvoiceScores);

            if (currentRoundScores.isEmpty()) {
                break;
            }

            // 选择评分最高的发票进行匹配
            InvoiceScore bestInvoice = currentRoundScores.get(0);

            List<MatchResult> invoiceResults = matchWithSingleInvoice(remainingDetails, bestInvoice);

            if (invoiceResults.isEmpty()) {
                remainingInvoiceScores.removeIf(score -> score.getInvoiceId().equals(bestInvoice.getInvoiceId()));
            } else {
                results.addAll(invoiceResults);

                // 移除已完全匹配的明细
                Set<String> matchedDetailIds = new HashSet<>();
                for (MatchResult result : invoiceResults) {
                    YwDjmx docDetail = result.getDocumentDetail();
                    BigDecimal remainingQuantity = docDetail.getSySl().subtract(result.getMatchQuantity());

                    if (remainingQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                        // 明细已完全匹配
                        matchedDetailIds.add(docDetail.getId());
                    } else {
                        // 更新明细的剩余数量
                        docDetail.setSySl(remainingQuantity);
                    }
                }

                remainingDetails.removeIf(detail -> matchedDetailIds.contains(detail.getId()));

                // 检查发票是否还有剩余可匹配的明细
                boolean invoiceHasRemaining = bestInvoice.getInvoiceDetails().stream()
                    .anyMatch(invDetail -> {
                        BigDecimal remaining = invDetail.getSySl();
                        return remaining != null && remaining.compareTo(BigDecimal.ZERO) > 0;
                    });

                if (!invoiceHasRemaining) {
                    remainingInvoiceScores.removeIf(score -> score.getInvoiceId().equals(bestInvoice.getInvoiceId()));
                }
            }

            round++;

            // 防止无限循环
            if (round > 100) {
                log.error("匹配轮次超过100轮，可能存在死循环，强制退出");
                break;
            }
        }

        log.info("动态重新评分的贪心匹配算法完成，总匹配结果数量：{}", results.size());
        return results;
    }

    /**
     * 重新计算发票评分（基于当前剩余状态）
     */
    private List<InvoiceScore> recalculateInvoiceScores(List<YwDjmx> remainingDetails, List<InvoiceScore> remainingInvoiceScores) {
        List<InvoiceScore> recalculatedScores = new ArrayList<>();

        for (InvoiceScore invoiceScore : remainingInvoiceScores) {
            // 过滤出仍有剩余数量的电票明细
            List<DpFpxxMx> availableInvoiceDetails = invoiceScore.getInvoiceDetails().stream()
                .filter(invDetail -> {
                    BigDecimal remaining = invDetail.getSySl();
                    return remaining != null && remaining.compareTo(BigDecimal.ZERO) > 0;
                })
                .collect(Collectors.toList());

            if (availableInvoiceDetails.isEmpty()) {
                continue; // 该发票已无可用明细
            }

            // 重新计算评分
            InvoiceScore newInvoiceScore = calculateSingleInvoiceScore(remainingDetails, invoiceScore.getInvoiceId(), availableInvoiceDetails);

            if (newInvoiceScore.getTotalScore() > 0) {
                recalculatedScores.add(newInvoiceScore);
            }
        }

        // 按评分降序排序
        recalculatedScores.sort((a, b) -> Double.compare(b.getTotalScore(), a.getTotalScore()));

        return recalculatedScores;
    }

    /**
     * 与单张发票进行匹配
     */
    private List<MatchResult> matchWithSingleInvoice(List<YwDjmx> documentDetails, InvoiceScore invoiceScore) {
        List<MatchResult> results = new ArrayList<>();
        List<DpFpxxMx> availableInvoiceDetails = new ArrayList<>(invoiceScore.getInvoiceDetails());

        for (YwDjmx docDetail : documentDetails) {
            MatchResult result = findBestMatchInInvoice(docDetail, availableInvoiceDetails);
            if (result != null) {
                results.add(result);
                // 更新电票明细的剩余数量（内存中）
                updateInvoiceDetailInMemory(result.getInvoiceDetail(), result.getMatchQuantity());
            }
        }
        return results;
    }

    /**
     * 在单张发票中寻找最佳匹配
     */
    private MatchResult findBestMatchInInvoice(YwDjmx docDetail, List<DpFpxxMx> invoiceDetails) {
        MatchResult bestResult = null;
        double bestScore = 0.0;

        for (DpFpxxMx invDetail : invoiceDetails) {
            // 检查剩余数量
            BigDecimal availableQuantity = invDetail.getSySl() != null ? invDetail.getSySl() : invDetail.getSpsl();
            if (availableQuantity == null || availableQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            double score = 0.0;
            String matchType = "2"; // 默认模糊匹配

            if (isExactMatch(docDetail, invDetail)) {
                score = 1.0;
                matchType = "1"; // 精确匹配
            } else {
                score = calculateMatchScore(docDetail, invDetail);
                if (score <= 0.6) continue; // 低于阈值跳过
            }

            if (score > bestScore) {
                BigDecimal matchQuantity = calculateMatchQuantity(docDetail, invDetail);
                if (matchQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    bestResult = new MatchResult();
                    bestResult.setDocumentDetail(docDetail);
                    bestResult.setInvoiceDetail(invDetail);
                    bestResult.setMatchQuantity(matchQuantity);
                    bestResult.setMatchScore(score);
                    bestResult.setMatchType(matchType);
                    bestScore = score;
                }
            }
        }

        return bestResult;
    }

    /**
     * 更新电票明细的内存中剩余数量
     */
    private void updateInvoiceDetailInMemory(DpFpxxMx invoiceDetail, BigDecimal matchQuantity) {
        BigDecimal currentRemaining = invoiceDetail.getSySl() != null ? invoiceDetail.getSySl() : invoiceDetail.getSpsl();
        if (currentRemaining != null) {
            BigDecimal newRemaining = currentRemaining.subtract(matchQuantity);
            invoiceDetail.setSySl(newRemaining);
        }
    }
    
    /**
     * 计算匹配得分
     */
    private double calculateMatchScore(YwDjmx docDetail, DpFpxxMx invDetail) {
        double score = 0.0;

        // 商品名称匹配（权重40%）
        if (StringUtils.isNotEmpty(docDetail.getSpMc()) && StringUtils.isNotEmpty(invDetail.getSpmc())) {
            if (docDetail.getSpMc().equals(invDetail.getSpmc())) {
                score += 0.4;
            } else if (docDetail.getSpMc().contains(invDetail.getSpmc()) ||
                      invDetail.getSpmc().contains(docDetail.getSpMc())) {
                score += 0.2;
            }
        }

        // 单价匹配（权重30%）
        if (docDetail.getDj() != null && invDetail.getDj() != null) {
            if (docDetail.getDj().compareTo(invDetail.getDj()) == 0) {
                score += 0.3;
            } else {
                // 允许一定的价格差异（5%以内）
                BigDecimal diff = docDetail.getDj().subtract(invDetail.getDj()).abs();
                BigDecimal tolerance = docDetail.getDj().multiply(new BigDecimal("0.05"));
                if (diff.compareTo(tolerance) <= 0) {
                    score += 0.15;
                }
            }
        }

        // 税率匹配（权重20%）
        if (docDetail.getSlv() != null && invDetail.getSl() != null) {
            if (docDetail.getSlv().compareTo(invDetail.getSl()) == 0) {
                score += 0.2;
            }
        }

        // 商品编码匹配（权重10%）
        if (StringUtils.isNotEmpty(docDetail.getSpBm()) && StringUtils.isNotEmpty(invDetail.getSpbm())) {
            if (docDetail.getSpBm().equals(invDetail.getSpbm())) {
                score += 0.1;
            }
        }

        return score;
    }

    /**
     * 判断是否为精确匹配
     * 精确匹配要求所有关键字段完全相等
     *
     * @param docDetail 单据明细
     * @param invDetail 电票明细
     * @return 是否为精确匹配
     */
    private boolean isExactMatch(YwDjmx docDetail, DpFpxxMx invDetail) {
        // 商品名称必须完全相等
        if (!StringUtils.isNotEmpty(docDetail.getSpMc()) || !StringUtils.isNotEmpty(invDetail.getSpmc()) ||
            !docDetail.getSpMc().equals(invDetail.getSpmc())) {
            return false;
        }

        // 单价必须完全相等
        if (docDetail.getDj() == null || invDetail.getDj() == null ||
            docDetail.getDj().compareTo(invDetail.getDj()) != 0) {
            return false;
        }

        // 税率必须完全相等
        if (docDetail.getSlv() == null || invDetail.getSl() == null ||
            docDetail.getSlv().compareTo(invDetail.getSl()) != 0) {
            return false;
        }

        // 商品编码如果都存在，必须完全相等
        if (StringUtils.isNotEmpty(docDetail.getSpBm()) && StringUtils.isNotEmpty(invDetail.getSpbm()) &&
            !docDetail.getSpBm().equals(invDetail.getSpbm())) {
            return false;
        }

        return true;
    }
    
    /**
     * 选择最佳匹配
     */
    private MatchCandidate selectBestMatch(List<MatchCandidate> candidates) {
        return candidates.get(0); // 已按得分排序，取第一个
    }
    
    /**
     * 计算匹配数量
     */
    private BigDecimal calculateMatchQuantity(YwDjmx docDetail, DpFpxxMx invDetail) {
        // 检查电票明细的剩余可匹配数量
        if (invDetail.getSySl() == null || invDetail.getSySl().compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        // 取单据明细剩余数量和电票明细剩余数量的最小值
        BigDecimal docRemaining = docDetail.getSySl();
        BigDecimal invRemaining = invDetail.getSySl();

        // 如果单据明细剩余数量为null，使用原始数量
        if (docRemaining == null) {
            docRemaining = docDetail.getSl();
        }

        // 如果仍然为null，返回0
        if (docRemaining == null) {
            return BigDecimal.ZERO;
        }

        return docRemaining.min(invRemaining);
    }
    
    // 其他辅助方法...
    
    /**
     * 发票评分结果类
     */
    private static class InvoiceScore {
        private String invoiceId;
        private List<DpFpxxMx> invoiceDetails;
        private double totalScore;
        private double coverageScore;      // 商品覆盖度评分
        private double quantityScore;     // 数量满足度评分
        private double exactMatchScore;   // 精确匹配度评分
        private double priorityScore;     // 优先级评分
        private int matchableProductCount; // 可匹配商品数量
        private BigDecimal matchableQuantity; // 可匹配总数量

        // getters and setters
        public String getInvoiceId() { return invoiceId; }
        public void setInvoiceId(String invoiceId) { this.invoiceId = invoiceId; }
        public List<DpFpxxMx> getInvoiceDetails() { return invoiceDetails; }
        public void setInvoiceDetails(List<DpFpxxMx> invoiceDetails) { this.invoiceDetails = invoiceDetails; }
        public double getTotalScore() { return totalScore; }
        public void setTotalScore(double totalScore) { this.totalScore = totalScore; }
        public double getCoverageScore() { return coverageScore; }
        public void setCoverageScore(double coverageScore) { this.coverageScore = coverageScore; }
        public double getQuantityScore() { return quantityScore; }
        public void setQuantityScore(double quantityScore) { this.quantityScore = quantityScore; }
        public double getExactMatchScore() { return exactMatchScore; }
        public void setExactMatchScore(double exactMatchScore) { this.exactMatchScore = exactMatchScore; }
        public double getPriorityScore() { return priorityScore; }
        public void setPriorityScore(double priorityScore) { this.priorityScore = priorityScore; }
        public int getMatchableProductCount() { return matchableProductCount; }
        public void setMatchableProductCount(int matchableProductCount) { this.matchableProductCount = matchableProductCount; }
        public BigDecimal getMatchableQuantity() { return matchableQuantity; }
        public void setMatchableQuantity(BigDecimal matchableQuantity) { this.matchableQuantity = matchableQuantity; }
    }

    /**
     * 匹配候选类
     */
    private static class MatchCandidate {
        private DpFpxxMx invoiceDetail;
        private double score;

        // getters and setters
        public DpFpxxMx getInvoiceDetail() { return invoiceDetail; }
        public void setInvoiceDetail(DpFpxxMx invoiceDetail) { this.invoiceDetail = invoiceDetail; }
        public double getScore() { return score; }
        public void setScore(double score) { this.score = score; }
    }
    
    /**
     * 匹配结果类
     */
    private static class MatchResult {
        private YwDjmx documentDetail;
        private DpFpxxMx invoiceDetail;
        private BigDecimal matchQuantity;
        private double matchScore;
        private String matchType;
        
        // getters and setters
        public YwDjmx getDocumentDetail() { return documentDetail; }
        public void setDocumentDetail(YwDjmx documentDetail) { this.documentDetail = documentDetail; }
        public DpFpxxMx getInvoiceDetail() { return invoiceDetail; }
        public void setInvoiceDetail(DpFpxxMx invoiceDetail) { this.invoiceDetail = invoiceDetail; }
        public BigDecimal getMatchQuantity() { return matchQuantity; }
        public void setMatchQuantity(BigDecimal matchQuantity) { this.matchQuantity = matchQuantity; }
        public double getMatchScore() { return matchScore; }
        public void setMatchScore(double matchScore) { this.matchScore = matchScore; }
        public String getMatchType() { return matchType; }
        public void setMatchType(String matchType) { this.matchType = matchType; }
    }
    
    // 辅助方法
    private void updateTaskStatus(String taskId, String status, String errorMsg) {
        PpRw task = new PpRw();
        task.setId(taskId);
        task.setRwZt(status);
        task.setErrorMsg(errorMsg);
        task.setUpdateBy(ShiroUtils.getLoginName());
        task.setUpdateTime(DateUtils.getNowDate());
        ppRwMapper.updatePpRw(task);
        
        // 更新缓存
        MatchProgressDto progress = taskProgressCache.get(taskId);
        if (progress != null) {
            progress.setStatus(status);
            progress.setStatusDesc(getStatusDescription(status));
            progress.setErrorMsg(errorMsg);
        }
    }
    
    private void updateProgress(String taskId, int processed, int success, int failed, int total) {
        MatchProgressDto progress = taskProgressCache.get(taskId);
        if (progress != null) {
            progress.setSuccessCount(success);
            progress.setFailedCount(failed);
            progress.setProgress(calculateProgress(success, failed, total));
            progress.setStatusDesc(String.format("已处理 %d/%d", processed, total));
        }
    }
    
    private int calculateProgress(int success, int failed, int total) {
        if (total == 0) return 0;
        return Math.round((float)(success + failed) / total * 100);
    }
    
    private String getStatusDescription(String status) {
        switch (status) {
            case "PENDING": return "等待执行";
            case "RUNNING": return "执行中";
            case "SUCCESS": return "执行成功";
            case "FAILED": return "执行失败";
            case "CANCELLED": return "已取消";
            default: return "未知状态";
        }
    }
    
    /**
     * 保存匹配结果
     */
    @Transactional(rollbackFor = Exception.class)
    private void saveMatchResults(String djId, List<MatchResult> matchResults) {
        try {
            for (MatchResult result : matchResults) {
                String djmxId = result.getDocumentDetail().getId();
                String fpmxId = result.getInvoiceDetail().getId();

                // 检查是否已存在相同的匹配记录
                YwDjFpPp existingMatch = ywDjFpPpMapper.selectByDjmxAndFpmx(djmxId, fpmxId);
                if (existingMatch != null) {
                    log.warn("匹配记录已存在，跳过保存 - 单据明细ID: {}, 发票明细ID: {}", djmxId, fpmxId);
                    continue;
                }

                // 创建匹配关系记录
                YwDjFpPp matchRelation = new YwDjFpPp();
                matchRelation.setId(IdUtils.fastSimpleUUID());
                matchRelation.setDjId(djId);
                matchRelation.setDjmxId(djmxId);
                matchRelation.setFpId(result.getInvoiceDetail().getFpid());
                matchRelation.setFpmxId(fpmxId);
                matchRelation.setPpSl(result.getMatchQuantity());

                // 计算匹配金额和税额
                BigDecimal matchAmount = result.getMatchQuantity().multiply(result.getInvoiceDetail().getDj());
                BigDecimal matchTax = matchAmount.multiply(result.getInvoiceDetail().getSl());
                matchRelation.setPpJe(matchAmount);
                matchRelation.setPpSe(matchTax);

                matchRelation.setPpDf(new BigDecimal(result.getMatchScore()));
                matchRelation.setPpLx(result.getMatchType());
                matchRelation.setPpZt("1"); // 有效
                matchRelation.setPpRq(DateUtils.getNowDate());
                matchRelation.setPpRy(ShiroUtils.getLoginName());
                matchRelation.setHcZt("0"); // 未红冲
                matchRelation.setCreateBy(ShiroUtils.getLoginName());
                matchRelation.setCreateTime(DateUtils.getNowDate());

                // 记录匹配结果日志
                log.info("保存匹配关系 - 单据明细ID：{}，电票明细ID：{}，匹配类型：{}，匹配得分：{}%，匹配数量：{}，匹配金额：{}",
                        djmxId, fpmxId, result.getMatchType(),
                        (result.getMatchScore() * 100), result.getMatchQuantity(), matchAmount);

                // 保存匹配关系
                ywDjFpPpMapper.insertYwDjFpPp(matchRelation);

                // 更新单据明细剩余数量
                updateDocumentDetailRemaining(result.getDocumentDetail(), result.getMatchQuantity());

                // 更新电票明细剩余数量
                updateInvoiceDetailRemaining(result.getInvoiceDetail(), result.getMatchQuantity());
            }

        } catch (Exception e) {
            log.error("保存匹配结果失败，单据ID：{}", djId, e);
            throw new RuntimeException("保存匹配结果失败", e);
        }
    }

    /**
     * 更新单据匹配状态
     */
    private void updateDocumentMatchStatus(String djId) {
        try {
            // 使用状态管理服务更新单据匹配状态
            matchStatusService.updateDocumentMatchStatus(djId);
            log.debug("单据匹配状态更新完成，单据ID：{}", djId);
        } catch (Exception e) {
            log.error("更新单据匹配状态失败，单据ID：{}", djId, e);
            throw new RuntimeException("更新单据匹配状态失败", e);
        }
    }

    /**
     * 更新发票匹配状态
     */
    private void updateInvoiceMatchStatus(String fpId) {
        try {
            // 使用状态管理服务更新发票匹配状态
            matchStatusService.updateInvoiceMatchStatus(fpId);
            log.debug("发票匹配状态更新完成，发票ID：{}", fpId);
        } catch (Exception e) {
            log.error("更新发票匹配状态失败，发票ID：{}", fpId, e);
            throw new RuntimeException("更新发票匹配状态失败", e);
        }
    }

    /**
     * 生成正数发票申请单
     */
    private void generatePositiveInvoiceApplication(String djId, YwDjxx document, List<YwDjmx> documentDetails) {
        try {
            log.info("开始生成正数发票申请单，单据ID：{}", djId);

            // 创建发票申请单主表
            YwFpsqd fpsqd = new YwFpsqd();
            fpsqd.setSwguid(IdUtils.fastSimpleUUID());
            fpsqd.setSwfpdate(DateUtils.getDate()); // 申请日期
            fpsqd.setSwcsflag(0L); // 重试标志
            fpsqd.setQyid(document.getGmfNsrsbh()); // 企业ID（购买方纳税人识别号）
            fpsqd.setYwdjdm(document.getDjlx()); // 业务单据代码
            fpsqd.setSfdj(1L); // 是否单据
            fpsqd.setDjbh(document.getDjbh()); // 单据编号
            fpsqd.setFplxdm("01"); // 发票类型代码（增值税专用发票）
            fpsqd.setKplx(0L); // 开票类型：0-正数发票
            fpsqd.setKpzt(0L); // 开票状态（待开票）
            fpsqd.setCreate_by(ShiroUtils.getLoginName());
            fpsqd.setCreate_time(DateUtils.getNowDate());

            // 创建发票申请单明细列表
            List<YwFpsqdmx> fpsqdmxList = new ArrayList<>();

            for (YwDjmx docDetail : documentDetails) {
                YwFpsqdmx fpsqdmx = new YwFpsqdmx();
                fpsqdmx.setSwguid(IdUtils.fastSimpleUUID());
                fpsqdmx.setSwmainguid(fpsqd.getSwguid()); // 主表ID

                // 从单据明细获取商品信息
                fpsqdmx.setSpmc(docDetail.getSpMc()); // 商品名称
                fpsqdmx.setSpbm(docDetail.getSpBm()); // 商品编码
                fpsqdmx.setGgxh(docDetail.getGgxh()); // 规格型号
                fpsqdmx.setDw(docDetail.getDw()); // 单位
                fpsqdmx.setSl(docDetail.getSl().longValue()); // 数量
                fpsqdmx.setDj(docDetail.getDj()); // 单价

                // 计算金额和税额
                BigDecimal amount = docDetail.getJe();
                BigDecimal tax = docDetail.getSe();
                BigDecimal totalAmount = amount.add(tax);

                fpsqdmx.setJe(amount); // 金额
                fpsqdmx.setSe(tax); // 税额
                fpsqdmx.setSjcjhsje(totalAmount); // 价税合计
                fpsqdmx.setSlv(docDetail.getSlv()); // 税率

                fpsqdmx.setCreate_by(ShiroUtils.getLoginName());
                fpsqdmx.setCreate_time(DateUtils.getNowDate());

                fpsqdmxList.add(fpsqdmx);
            }

            // 设置明细到主表
            fpsqd.setYwFpsqdmxList(fpsqdmxList);

            // 保存发票申请单
            ywFpsqdService.insertYwFpsqd(fpsqd);

            log.info("正数发票申请单生成成功，单据ID：{}，申请单ID：{}", djId, fpsqd.getSwguid());

        } catch (Exception e) {
            log.error("生成正数发票申请单失败，单据ID：{}", djId, e);
            throw new RuntimeException("生成正数发票申请单失败", e);
        }
    }

    /**
     * 按发票分组生成红冲发票申请单
     */
    private void generateRedFlushInvoiceApplicationsByInvoice(String djId, YwDjxx document, List<MatchResult> matchResults) {
        try {
            log.info("开始按发票分组生成红冲发票申请单，单据ID：{}", djId);

            // 按发票ID分组匹配结果
            Map<String, List<MatchResult>> invoiceGroupedResults = matchResults.stream()
                .collect(Collectors.groupingBy(result -> result.getInvoiceDetail().getFpid()));

            log.info("匹配结果按发票分组，共{}张发票需要生成红冲申请单", invoiceGroupedResults.size());

            // 调试信息：打印分组详情
            for (Map.Entry<String, List<MatchResult>> entry : invoiceGroupedResults.entrySet()) {
                log.info("发票ID：{}，匹配结果数量：{}", entry.getKey(), entry.getValue().size());
            }

            // 为每张发票生成一个红冲申请单
            for (Map.Entry<String, List<MatchResult>> entry : invoiceGroupedResults.entrySet()) {
                String invoiceId = entry.getKey();
                List<MatchResult> invoiceMatchResults = entry.getValue();

                // 获取发票信息
                DpFpxx invoice = dpFpxxMapper.selectDpFpxxById(invoiceId);
                if (invoice == null) {
                    log.error("发票不存在，发票ID：{}", invoiceId);
                    continue;
                }

                generateSingleRedFlushInvoiceApplication(djId, document, invoice, invoiceMatchResults);
            }

            log.info("按发票分组生成红冲发票申请单完成，单据ID：{}", djId);

        } catch (Exception e) {
            log.error("按发票分组生成红冲发票申请单失败，单据ID：{}", djId, e);
            throw new RuntimeException("按发票分组生成红冲发票申请单失败", e);
        }
    }

    /**
     * 生成单张发票的红冲申请单
     */
    private void generateSingleRedFlushInvoiceApplication(String djId, YwDjxx document, DpFpxx invoice, List<MatchResult> matchResults) {
        try {
            log.info("开始生成单张发票的红冲申请单，单据ID：{}，发票ID：{}，匹配结果数量：{}", djId, invoice.getId(), matchResults.size());

            // 创建发票申请单主表
            YwFpsqd fpsqd = new YwFpsqd();
            fpsqd.setSwguid(IdUtils.fastSimpleUUID());
            fpsqd.setSwfpdate(DateUtils.getDate()); // 申请日期
            fpsqd.setSwcsflag(0L); // 重试标志
            fpsqd.setQyid(document.getGmfNsrsbh()); // 企业ID（购买方纳税人识别号）
            fpsqd.setYwdjdm(document.getDjlx()); // 业务单据代码
            fpsqd.setSfdj(1L); // 是否单据
            fpsqd.setDjbh(document.getDjbh()); // 单据编号
            fpsqd.setFplxdm("01"); // 发票类型代码（增值税专用发票）
            fpsqd.setKplx(1L); // 开票类型：1-红冲
            fpsqd.setKpzt(0L); // 开票状态（待开票）

            // 设置原发票信息（红冲申请单必须字段）
            fpsqd.setYfpdm(invoice.getFpDm()); // 原发票代码
            fpsqd.setYfphm(invoice.getFpHm()); // 原发票号码

            fpsqd.setCreate_by(ShiroUtils.getLoginName());
            fpsqd.setCreate_time(DateUtils.getNowDate());

            // 创建发票申请单明细列表
            List<YwFpsqdmx> fpsqdmxList = new ArrayList<>();

            // 使用Map来去重，避免重复的申请单明细
            Map<String, YwFpsqdmx> uniqueDetailsMap = new HashMap<>();

            log.info("开始处理{}条匹配结果生成申请单明细", matchResults.size());
            for (MatchResult result : matchResults) {
                YwDjmx docDetail = result.getDocumentDetail();

                // 创建唯一键：商品名称+商品编码+规格型号+单位+单价+税率
                String uniqueKey = String.format("%s_%s_%s_%s_%s_%s",
                    docDetail.getSpMc() != null ? docDetail.getSpMc() : "",
                    docDetail.getSpBm() != null ? docDetail.getSpBm() : "",
                    docDetail.getGgxh() != null ? docDetail.getGgxh() : "",
                    docDetail.getDw() != null ? docDetail.getDw() : "",
                    docDetail.getDj() != null ? docDetail.getDj().toString() : "",
                    docDetail.getSlv() != null ? docDetail.getSlv().toString() : "");

                YwFpsqdmx existingDetail = uniqueDetailsMap.get(uniqueKey);
                if (existingDetail != null) {
                    // 如果已存在相同商品，累加数量和金额
                    long newQuantity = existingDetail.getSl() + result.getMatchQuantity().longValue();
                    existingDetail.setSl(newQuantity);

                    // 重新计算金额和税额
                    BigDecimal amount = docDetail.getDj().multiply(new BigDecimal(newQuantity));
                    BigDecimal taxRate = docDetail.getSlv();
                    BigDecimal tax = amount.multiply(taxRate);

                    existingDetail.setJe(amount);
                    existingDetail.setSe(tax);

                    log.info("合并重复明细：{}，新数量：{}", docDetail.getSpMc(), newQuantity);
                } else {
                    // 创建新的申请单明细
                    YwFpsqdmx fpsqdmx = new YwFpsqdmx();
                    fpsqdmx.setSwguid(IdUtils.fastSimpleUUID());
                    fpsqdmx.setSwmainguid(fpsqd.getSwguid()); // 主表ID

                    // 从单据明细获取商品信息
                    fpsqdmx.setSpmc(docDetail.getSpMc()); // 商品名称
                    fpsqdmx.setSpbm(docDetail.getSpBm()); // 商品编码
                    fpsqdmx.setGgxh(docDetail.getGgxh()); // 规格型号
                    fpsqdmx.setDw(docDetail.getDw()); // 单位
                    fpsqdmx.setSl(result.getMatchQuantity().longValue()); // 数量（匹配数量）
                    fpsqdmx.setDj(docDetail.getDj()); // 单价

                    // 计算金额和税额
                    BigDecimal amount = docDetail.getDj().multiply(result.getMatchQuantity());
                    BigDecimal taxRate = docDetail.getSlv();
                    BigDecimal tax = amount.multiply(taxRate);

                    fpsqdmx.setJe(amount); // 金额
                    fpsqdmx.setSlv(taxRate); // 税率
                    fpsqdmx.setSe(tax); // 税额

                    fpsqdmx.setCreate_by(ShiroUtils.getLoginName());
                    fpsqdmx.setCreate_time(DateUtils.getNowDate());

                    uniqueDetailsMap.put(uniqueKey, fpsqdmx);
                }
            }

            // 将去重后的明细添加到列表
            fpsqdmxList.addAll(uniqueDetailsMap.values());

            // 设置明细列表
            fpsqd.setYwFpsqdmxList(fpsqdmxList);

            // 保存发票申请单
            ywFpsqdService.insertYwFpsqd(fpsqd);

            log.info("单张发票的红冲申请单生成成功，单据ID：{}，发票ID：{}，申请单ID：{}，原发票代码：{}，原发票号码：{}",
                    djId, invoice.getId(), fpsqd.getSwguid(), invoice.getFpDm(), invoice.getFpHm());

        } catch (Exception e) {
            log.error("生成单张发票的红冲申请单失败，单据ID：{}，发票ID：{}", djId, invoice.getId(), e);
            throw new RuntimeException("生成单张发票的红冲申请单失败", e);
        }
    }

    /**
     * 更新单据状态
     */
    private void updateDocumentStatus(String djId, String status) {
        try {
            YwDjxx document = new YwDjxx();
            document.setId(djId);
            document.setPpzt(status); // 设置处理状态
            document.setUpdateBy(ShiroUtils.getLoginName());
            document.setUpdateTime(DateUtils.getNowDate());

            ywDjxxMapper.updateYwDjxx(document);
            log.info("单据状态更新成功，单据ID：{}，状态：{}", djId, status);

        } catch (Exception e) {
            log.error("更新单据状态失败，单据ID：{}", djId, e);
            throw new RuntimeException("更新单据状态失败", e);
        }
    }

    /**
     * 更新单据明细剩余数量
     */
    private void updateDocumentDetailRemaining(YwDjmx documentDetail, BigDecimal matchQuantity) {
        // 获取当前剩余数量，如果为null则使用原始数量
        BigDecimal currentRemaining = documentDetail.getSySl();
        if (currentRemaining == null) {
            currentRemaining = documentDetail.getSl();
        }

        // 如果仍然为null，跳过更新
        if (currentRemaining == null) {
            log.warn("单据明细数量为null，跳过剩余数量更新 - 明细ID: {}", documentDetail.getId());
            return;
        }

        BigDecimal newRemaining = currentRemaining.subtract(matchQuantity);
        BigDecimal unitPrice = documentDetail.getDj();
        BigDecimal taxRate = documentDetail.getSlv();

        // 计算剩余金额和税额
        BigDecimal remainingAmount = newRemaining.multiply(unitPrice);
        BigDecimal remainingTax = remainingAmount.multiply(taxRate);

        YwDjmx updateDetail = new YwDjmx();
        updateDetail.setId(documentDetail.getId());
        updateDetail.setSySl(newRemaining);
        updateDetail.setSyJe(remainingAmount);
        updateDetail.setSySe(remainingTax);
        // 处理匹配次数，如果为null则从0开始
        Long currentPpCs = documentDetail.getPpCs();
        updateDetail.setPpCs(currentPpCs == null ? 1L : currentPpCs + 1L);
        updateDetail.setLastPpTime(DateUtils.getNowDate());

        // 更新匹配状态
        if (newRemaining.compareTo(BigDecimal.ZERO) == 0) {
            updateDetail.setPpZt("2"); // 完全匹配
        } else {
            updateDetail.setPpZt("1"); // 部分匹配
        }

        updateDetail.setUpdateBy(ShiroUtils.getLoginName());
        updateDetail.setUpdateTime(DateUtils.getNowDate());

        ywDjxxMapper.updateYwDjmx(updateDetail);
    }

    /**
     * 更新电票明细剩余数量
     */
    private void updateInvoiceDetailRemaining(DpFpxxMx invoiceDetail, BigDecimal matchQuantity) {
        BigDecimal currentRemaining = invoiceDetail.getSySl();

        // 如果剩余数量为null，跳过更新
        if (currentRemaining == null) {
            log.warn("电票明细剩余数量为null，跳过剩余数量更新 - 明细ID: {}", invoiceDetail.getId());
            return;
        }

        BigDecimal newRemaining = currentRemaining.subtract(matchQuantity);
        BigDecimal unitPrice = invoiceDetail.getDj();

        // 计算剩余金额（税额通过 getSySe() 方法计算）
        BigDecimal remainingAmount = newRemaining.multiply(unitPrice);

        invoiceDetail.setSySl(newRemaining);
        invoiceDetail.setSyJe(remainingAmount);
        invoiceDetail.setLastPpTime(DateUtils.getNowDate());
        invoiceDetail.setUpdateTime(DateUtils.getNowDate());

        dpFpxxMapper.updateDpFpxxMx(invoiceDetail);
    }

}

